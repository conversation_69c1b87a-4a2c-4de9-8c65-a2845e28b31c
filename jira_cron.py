import os
import time
import schedule
import dotenv
import asyncio

# Load environment variables from .env file
dotenv.load_dotenv()

from typing import List, Dict, Any
from jira_client import <PERSON>ra<PERSON><PERSON>
from jira_parser import parse_comment, <PERSON>raComment, JiraIssue
from agents.monitoring import MonitoringAgent
from minisweagent.models.litellm_model import LitellmModel
from minisweagent.environments.local import LocalEnvironment
from minisweagent.utils.log import logger


logger.info(f"JIRA_BASE_URL: {os.getenv('JIRA_BASE_URL')}")


async def fetch_and_process_comments():
    """
    Fetch comments from Jira and process AI tasks
    """
    logger.info("Checking for new Jira comments...")
    client = JiraClient()

    # Get JQL from environment variable with fallback
    jql = os.getenv("JIRA_JQL", "commentDate >= -1d")
    try:
        issues = await client.search({"jql": jql})
    except Exception as error:
        logger.error(f"Failed to fetch Jira issues: {str(error)}")
        return

    for issue in issues.get("issues", []):
        issue_key = issue["key"]
        try:
            comments = await client.get_comments(issue_key)
        except Exception as error:
            logger.error(f"Failed to fetch comments for issue {issue_key}: {str(error)}")
            continue

        for comment in comments:
            parsed = parse_comment(comment, issue)
            if parsed["is_ai_task"] and parsed["task_info"]:
                task = parsed["task_info"]
                logger.info(f"Found AI task in issue {issue_key}: {task['task_description']}")
                assign_to_agent(task)


from agents.monitoring import MonitoringAgent

def assign_to_agent(task: Dict[str, Any]):
    """
    Assign the task to the appropriate agent
    """

    description = task["task_description"]
    branch = task["branch"]
    issue_key = task.get("issue_key", "unknown")
    comment_id = task.get("comment_id", "unknown")

    try:

        # Step 1: Setup Git branches
        repo_path = os.getenv("REPO_PATH", ".")
        task_branch = setup_for_ai_task(
            task.get("version", "unknown"),
            issue_key,
            comment_id,
            repo_path,
        )

        # Step 2: Run AI agent

        # Initialize and run the agent
        model_name = "openrouter/moonshotai/kimi-k2:free"
        agent = MonitoringAgent(
            LitellmModel(model_name=model_name),
            LocalEnvironment(),
        )
        agent.run(description)

        # Step 3: Post comment to Jira (mock)
        return {"success": True, "branch": task_branch}

    except Exception as error:
        return {"success": False, "error": str(error)}


if __name__ == "__main__":
    # Get sync interval from environment variable with fallback (5 minutes)
    sync_interval = int(os.getenv("JIRA_SYNC_INTERVAL_MINUTES", "5"))
    schedule.every(sync_interval).minutes.do(lambda: asyncio.run(fetch_and_process_comments()))

    logger.info(
        f"Jira comment monitor started (sync interval: {sync_interval} minutes). Press Ctrl+C to exit."
    )

    try:
        while True:
            schedule.run_pending()
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("Stopping Jira comment monitor...")
