import os
import base64
import aiohttp
from typing import Dict, Any, Optional, List, TypedDict

class JiraConfig(TypedDict):
    base_url: str
    api_token: str
    home: str
    username: str
    password: str
    authorization: str
    jsessionid: str
    cookie: str

class JiraClient:
    """
    Client for interacting with the Jira API
    """

    def __init__(self):
        self.config: JiraConfig = {
            "base_url": os.getenv("JIRA_BASE_URL", ""),
            "api_token": os.getenv("JIRA_API_TOKEN", ""),
            "home": os.getenv("JIRA_HOME", ""),
            "username": os.getenv("JIRA_USERNAME", ""),
            "password": os.getenv("JIRA_PASSWORD", ""),
            "authorization": os.getenv("JIRA_AUTHORIZATION", ""),
            "jsessionid": os.getenv("JIRA_JSESSIONID", ""),
            "cookie": os.getenv("JIRA_COOKIE", ""),
        }

    def _get_auth_headers(self) -> Dict[str, str]:
        """
        Get Jira authentication headers from config
        """
        headers = {
            "Content-Type": "application/json",
            "Host": self.config["home"].split("://")[1] if self.config["home"] else "",
            "Origin": self.config["home"] if self.config["home"] else "",
            "Referer": self.config["home"] if self.config["home"] else "",
        }

        # Determine authentication method based on available config
        if self.config["authorization"]:
            # Basic auth with authorization token
            headers["Authorization"] = f"Basic {self.config['authorization']}"
        elif self.config["username"] and self.config["password"]:
            # Basic auth with username/password
            credentials = f"{self.config['username']}:{self.config['password']}"
            encoded_credentials = base64.b64encode(credentials.encode("utf-8")).decode("utf-8")
            headers["Authorization"] = f"Basic {encoded_credentials}"
        elif self.config["cookie"]:
            # Cookie-based auth with custom cookie
            headers["Cookie"] = self.config["cookie"]
        elif self.config["jsessionid"]:
            # Cookie-based auth with JSESSIONID
            headers["Cookie"] = f"JSESSIONID={self.config['jsessionid']}"
        else:
            # Fallback to API token
            headers["Authorization"] = f"Bearer {self.config['api_token']}"

        return headers

    async def request(self, method: str, path: str, data: Optional[Dict[str, Any]] = None) -> Any:
        """
        Make an async request to the Jira API
        """
        url = f"{self.config['base_url']}{path}"
        headers = self._get_auth_headers()

        async with aiohttp.ClientSession() as session:
            async with session.request(
                method,
                url,
                headers=headers,
                json=data,
            ) as response:
                if not response.ok:
                    try:
                        error_data = await response.json()
                        error_messages = error_data.get("errorMessages", ["An error occurred"])
                        raise JiraError(", ".join(error_messages))
                    except ValueError:
                        # Handle invalid JSON response (e.g., HTML error page)
                        response_text = await response.text()
                        raise JiraError(f"Invalid response from Jira API: {response_text}")

                try:
                    return await response.json()
                except ValueError:
                    response_text = await response.text()
                    raise JiraError(f"Failed to parse JSON response: {response_text}")

    async def search(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Search for Jira issues
        """
        return await self.request("POST", "/rest/api/2/search", parameters)

    async def get_issue(self, issue_key: str) -> Dict[str, Any]:
        """
        Get a Jira issue by key
        """
        return await self.request("GET", f"/rest/api/2/issue/{issue_key}")

    async def get_comments(self, issue_key: str) -> List[Dict[str, Any]]:
        """
        Get comments for a Jira issue
        """
        response = await self.request("GET", f"/rest/api/2/issue/{issue_key}/comment")
        return response.get("comments", [])

    async def add_comment(self, issue_key: str, body: str) -> Dict[str, Any]:
        """
        Add a comment to a Jira issue
        """
        return await self.request("POST", f"/rest/api/2/issue/{issue_key}/comment", {"body": body})


class JiraError(Exception):
    """Custom exception for Jira API errors"""
    pass