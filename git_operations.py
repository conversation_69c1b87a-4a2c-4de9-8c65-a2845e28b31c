import subprocess
from typing import Optional, Callable


def log(message: str: Optional[Callable[[str], None]] = None) -> None:
    """Log a message using the provided callback."""
    if log_callback:
        log_callback(message)


async def execute_git_command(
    command: str,
    args: list[str],
    repo_path: str,
    log_callback: Optional[Callable[[str], None]] = None,
) -> str:
    """Execute a Git command and return the output."""
    log(f"Executing: git {command} {' '.join(args)}")

    try:
        result = subprocess.run(
            ["git", command] + args,
            cwd=repo_path,
            capture_output=True,
            text=True,
            check=True,
        )
        if result.stderr:
            log(f"stderr: {result.stderr}")
        log(f"stdout: {result.stdout}")
        return result.stdout
    except subprocess.CalledProcessError as error:
        log(f"Error: {error.stderr}")
        raise error


async def checkout_branch(
    branch_name: str,
    repo_path: str,
    log_callback: Optional[Callable[[str], None]] = None,
) -> None:
    """Checkout a Git branch. Create it if it doesn't exist."""
    try:
        await execute_git_command("checkout", [branch_name], repo_path)
        log(f"Successfully checked out branch: {branch_name}")
    except subprocess.CalledProcessError:
        log(f"Branch {branch_name} does not exist. Will create it.")
        await create_branch(branch_name, repo_path)


async def create_branch(
    branch_name: str,
    repo_path: str,
    log_callback: Optional[Callable[[str], None]] = None,
) -> None:
    """Create and checkout a new Git branch."""
    await execute_git_command("checkout", ["-b", branch_name], repo_path)
    log(f"Successfully created and checked out branch: {branch_name}")


async def has_remote(
    repo_path: str,
    log_callback: Optional[Callable[[str], None]] = None,
) -> bool:
    """Check if the repository has a remote."""
    try:
        output = await execute_git_command("remote", [], repo_path)
        return output.strip() != ""
    except subprocess.CalledProcessError:
        log("Error checking for remote, assuming no remote exists")
        return False


async def pull_rebase(
    repo_path: str,
    log_callback: Optional[Callable[[str], None]] = None,
) -> None:
    """Pull changes with rebase."""
    if await has_remote(repo_path):
        try:
            await execute_git_command("pull", ["-r"], repo_path)
            log("Successfully pulled with rebase")
        except subprocess.CalledProcessError as error:
            log(
                f"Error during pull: {error.stderr}. Continuing without pull.",
                log_callback,
            )
    else:
        log("Repository has no remote, skipping git pull")


async def get_current_branch(
    repo_path: str,
    log_callback: Optional[Callable[[str], None]] = None,
) -> str:
    """Get the current Git branch name."""
    output = await execute_git_command(
        "branch", ["--show-current"], repo_path
    )
    return output.strip()


async def branch_exists(
    branch_name: str,
    repo_path: str,
    log_callback: Optional[Callable[[str], None]] = None,
) -> bool:
    """Check if a Git branch exists."""
    try:
        output = await execute_git_command(
            "branch", ["--list", branch_name], repo_path
        )
        return output.strip() != ""
    except subprocess.CalledProcessError:
        return False


async def setup_for_ai_task(
    fix_version: str,
    issue_key: str,
    comment_id: str,
    repo_path: str,
) -> str:
    """Setup Git branches for an AI task."""
    # Step 1: Checkout the base branch
    base_branch = f"version/{fix_version}"
    await checkout_branch(base_branch, repo_path)
    await pull_rebase(repo_path)

    # Step 2: Checkout to issue branch
    issue_branch = f"issue/{issue_key}"
    if await branch_exists(issue_branch, repo_path):
        await checkout_branch(issue_branch, repo_path)
        await pull_rebase(repo_path)
    else:
        await create_branch(issue_branch, repo_path)

    # Step 3: Checkout to a new task branch
    task_branch = f"task/{issue_key}-{comment_id}"
    await create_branch(task_branch, repo_path)

    return task_branch
