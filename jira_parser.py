from typing import Dict, Any, Optional, TypedDict

# Constants
AI_TASK_MARKER = "```ai-task"
CODE_BLOCK_END = "```"


class JiraComment(TypedDict):
    """Type definition for Jira comment"""
    body: str


class JiraIssue(TypedDict):
    """Type definition for Jira issue"""
    key: str
    fields: Dict[str, Any]


class AITaskComment(TypedDict):
    """Type definition for parsed AI task"""
    issue_key: str
    version: str
    task_description: str
    ai_tool: str
    branch: str


class ParsedAIComment(TypedDict):
    """Type definition for parsed comment"""
    is_ai_task: bool
    task_info: Optional[AITaskComment]
    original_comment: <PERSON><PERSON><PERSON>om<PERSON>


def parse_comment(comment: <PERSON><PERSON>Comment, issue: JiraIssue) -> ParsedAIComment:
    """
    Parse a Jira comment to check if it contains an AI task
    """
    if AI_TASK_MARKER not in comment["body"]:
        return {
            "is_ai_task": False,
            "task_info": None,
            "original_comment": comment,
        }

    try:
        task_block = extract_task_block(comment["body"])
        if not task_block:
            return {
                "is_ai_task": False,
                "task_info": None,
                "original_comment": comment,
            }

        task_info = parse_task_block(task_block, issue)

        return {
            "is_ai_task": True,
            "task_info": task_info,
            "original_comment": comment,
        }
    except Exception as error:
        logger.error(f"Error parsing AI task comment: {error}")
        return {
            "is_ai_task": False,
            "task_info": None,
            "original_comment": comment,
        }


def extract_task_block(comment_body: str) -> Optional[str]:
    """
    Extract the AI task block from a comment body
    """
    start_index = comment_body.find(AI_TASK_MARKER)
    if start_index == -1:
        return None

    end_index = comment_body.find(
        CODE_BLOCK_END, start_index + len(AI_TASK_MARKER)
    )
    if end_index == -1:
        return None

    return comment_body[start_index + len(AI_TASK_MARKER) : end_index].strip()


def parse_task_block(block: str, issue: JiraIssue) -> AITaskComment:
    """
    Parse the task block to extract tool, description, etc.
    """
    lines = block.split("\n")
    ai_tool = "aider"  # default tool
    description: list[str] = []
    is_description = False

    # Get version from fixVersions, default to the first one
    version = (
        issue["fields"].get("fixVersions", [{}])[0].get("name", "3.11.1")
    )  # fallback version

    for line in lines:
        trimmed_line = line.strip()

        if trimmed_line.startswith("tool:"):
            ai_tool = trimmed_line[len("tool:") :].strip()
        elif trimmed_line.startswith("description:"):
            is_description = True
            if len(trimmed_line) > len("description:"):
                description.append(
                    trimmed_line[len("description:") :].strip()
                )
        elif is_description and trimmed_line:
            description.append(trimmed_line)
    
    branch = f"{version}_dev_{issue['key']}"

    return {
        "issue_key": issue["key"],
        "version": version,
        "task_description": "\n".join(description).strip(),
        "ai_tool": ai_tool,
        "branch": branch,
    }